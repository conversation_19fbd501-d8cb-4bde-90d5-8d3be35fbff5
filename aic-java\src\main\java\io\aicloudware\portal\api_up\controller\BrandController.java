package io.aicloudware.portal.api_up.controller;

import io.aicloudware.portal.api_up.service.IUpSystemConfigService;
import io.aicloudware.portal.framework.bean.ResponseBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigBean;
import io.aicloudware.portal.framework.sdk.bean.UpSystemConfigListBean;
import io.aicloudware.portal.framework.sdk.bean.UpLicenseBean;
import io.aicloudware.portal.framework.sdk.bean.UpLicenseListBean;
import io.aicloudware.portal.framework.sdk.contants.UpSystemConfigKey;
import io.aicloudware.portal.framework.utility.AssertUtil;
import io.aicloudware.portal.framework.utility.EncryptUtil;
import io.aicloudware.portal.framework.utility.Utility;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import io.swagger.annotations.ApiResponse;
import io.swagger.annotations.ApiResponses;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/brand")
public class BrandController {

    @Autowired
    private IUpSystemConfigService upSystemConfigService;

    @RequestMapping(value = "/", method = RequestMethod.POST)
    @ApiOperation(notes = "/", httpMethod = "POST", value = "设定品牌")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "", response = String.class)})
    @ResponseBody
    public ResponseBean setBrand(@ApiParam(value = "查询条件") @RequestBody Map<String, String> brand)  {
        AssertUtil.check(brand, "品牌不能为空");
        if (brand.get("title") != null) {
            UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_title);
            bean.setValue(brand.get("title"));
            UpSystemConfigListBean listBean = new UpSystemConfigListBean();
            listBean.setDataList(new UpSystemConfigBean[]{bean});
            upSystemConfigService.save(listBean);
            upSystemConfigService.refreshConfig(UpSystemConfigKey.system_title);
        }
        if (brand.get("logo") != null) {
            UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_logo);
            bean.setValue(brand.get("logo"));
            UpSystemConfigListBean listBean = new UpSystemConfigListBean();
            listBean.setDataList(new UpSystemConfigBean[]{bean});
            upSystemConfigService.save(listBean);
            upSystemConfigService.refreshConfig(UpSystemConfigKey.system_logo);
        }
        return ResponseBean.success(true);
    }

    @RequestMapping(value = "/title", method = RequestMethod.GET)
    @ApiOperation(notes = "/title", httpMethod = "GET", value = "获取品牌TITLE")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean getCode()  {
        UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_title);
        if (Utility.isNotEmpty(bean.getValue())) {
            return ResponseBean.success(bean.getValue());
        }
        return ResponseBean.success("欢迎来到AI云管家");
    }

    @RequestMapping(value = "/logo", method = RequestMethod.GET)
    @ApiOperation(notes = "/logo", httpMethod = "GET", value = "获取品牌LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean getLogo()  {
        UpSystemConfigBean bean = upSystemConfigService.get(UpSystemConfigKey.system_logo);
        if (Utility.isNotEmpty(bean.getValue())) {
            return ResponseBean.success(bean.getValue());
        }
        return ResponseBean.success("default");
    }

    @RequestMapping(value = "/logoList", method = RequestMethod.GET)
    @ApiOperation(notes = "/logoList", httpMethod = "GET", value = "获取品牌LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回组织ID", response = Integer.class)})
    @ResponseBody
    public ResponseBean logoList()  {
        return ResponseBean.success(upSystemConfigService.logoList());
    }

    @RequestMapping(value = "/uploadLogo/full", method = RequestMethod.POST)
    @ApiOperation(notes = "/uploadLogo/full", httpMethod = "POST", value = "上传完整版LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "上传成功", response = String.class)})
    @ResponseBody
    public ResponseBean uploadFullLogo(@ApiParam(value = "LOGO文件") @RequestParam("file") MultipartFile file) {
        try {
            String result = upSystemConfigService.uploadFullLogo(file);
            return ResponseBean.success(result);
        } catch (IllegalArgumentException e) {
            return ResponseBean.error(400, "参数错误", e.getMessage());
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }

    @RequestMapping(value = "/uploadLogo/mini", method = RequestMethod.POST)
    @ApiOperation(notes = "/uploadLogo/mini", httpMethod = "POST", value = "上传迷你版LOGO")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "上传成功", response = String.class)})
    @ResponseBody
    public ResponseBean uploadMiniLogo(@ApiParam(value = "LOGO文件") @RequestParam("file") MultipartFile file) {
        try {
            String result = upSystemConfigService.uploadMiniLogo(file);
            return ResponseBean.success(result);
        } catch (IllegalArgumentException e) {
            return ResponseBean.error(400, "参数错误", e.getMessage());
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }

    @RequestMapping(value = "/license/save", method = RequestMethod.POST)
    @ApiOperation(notes = "/license/save", httpMethod = "POST", value = "保存许可证配置")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "保存成功", response = String.class)})
    @ResponseBody
    public ResponseBean saveLicense(@ApiParam(value = "许可证列表") @RequestBody UpLicenseListBean licenseListBean) {
        try {
            AssertUtil.check(licenseListBean, "许可证列表不能为空");
            AssertUtil.check(licenseListBean.getDataList(), "许可证数据不能为空");

            // 将所有license文本用分号分割，合并为字符串
            StringBuilder licenseConfigBuilder = new StringBuilder();
            UpLicenseBean[] licenses = licenseListBean.getDataList();

            for (int i = 0; i < licenses.length; i++) {
                UpLicenseBean license = licenses[i];
                if (license != null && Utility.isNotEmpty(license.getLicenseCode())) {
                    if (i > 0) {
                        licenseConfigBuilder.append(";");
                    }
                    licenseConfigBuilder.append(license.getLicenseCode());
                }
            }

            // 存入UpSystemConfig
            UpSystemConfigBean configBean = upSystemConfigService.get(UpSystemConfigKey.license_config);
            configBean.setValue(licenseConfigBuilder.toString());
            UpSystemConfigListBean configListBean = new UpSystemConfigListBean();
            configListBean.setDataList(new UpSystemConfigBean[]{configBean});
            upSystemConfigService.save(configListBean);
            upSystemConfigService.refreshConfig(UpSystemConfigKey.license_config);

            return ResponseBean.success("许可证配置保存成功");
        } catch (IllegalArgumentException e) {
            return ResponseBean.error(400, "参数错误", e.getMessage());
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }

    @RequestMapping(value = "/license/list", method = RequestMethod.GET)
    @ApiOperation(notes = "/license/list", httpMethod = "GET", value = "获取许可证列表")
    @ApiResponses(value = {@ApiResponse(code = 200, message = "返回许可证列表", response = UpLicenseListBean.class)})
    @ResponseBody
    public ResponseBean getLicenseList() {
        try {
            // 从UpSystemConfig获取license_config的值
            UpSystemConfigBean configBean = upSystemConfigService.get(UpSystemConfigKey.license_config);
            String licenseConfigValue = configBean.getValue();

            UpLicenseListBean licenseListBean = new UpLicenseListBean();
            List<UpLicenseBean> licenseList = new ArrayList<>();

            if (Utility.isNotEmpty(licenseConfigValue)) {
                // 用分号分割license字符串
                String[] licenseTexts = licenseConfigValue.split(";");

                for (String licenseText : licenseTexts) {
                    if (Utility.isNotEmpty(licenseText.trim())) {
                        try {
                            // 用EncryptUtil.decryptWithRSA解密
                            String decryptedText = EncryptUtil.decryptWithRSA(licenseText.trim());

                            // 解密后的字符串格式是 {{MAC}} + "|" + {{expireDt}}
                            if (Utility.isNotEmpty(decryptedText) && decryptedText.contains("|")) {
                                String[] parts = decryptedText.split("\\|", 2);
                                if (parts.length == 2) {
                                    UpLicenseBean licenseBean = new UpLicenseBean();
                                    licenseBean.setName(parts[0]); // MAC作为名称
                                    licenseBean.setExpireDt(parts[1]); // 到期日期
                                    licenseBean.setLicenseCode(licenseText.trim()); // 原始license文本
                                    licenseList.add(licenseBean);
                                }
                            }
                        } catch (Exception e) {
                            // 解密失败时，仍然添加到列表中，但只有license文本
                            UpLicenseBean licenseBean = new UpLicenseBean();
                            licenseBean.setName("解密失败");
                            licenseBean.setExpireDt("");
                            licenseBean.setLicenseCode(licenseText.trim());
                            licenseList.add(licenseBean);
                        }
                    }
                }
            }

            licenseListBean.setDataList(licenseList.toArray(new UpLicenseBean[0]));
            return ResponseBean.success(licenseListBean);
        } catch (Exception e) {
            return ResponseBean.error(500, "系统错误", e.getMessage());
        }
    }
}
