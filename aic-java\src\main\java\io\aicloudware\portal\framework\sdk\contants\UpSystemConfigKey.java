package io.aicloudware.portal.framework.sdk.contants;

import io.aicloudware.portal.framework.utility.Utility;

public enum UpSystemConfigKey {

    ai_url("ai_url", true, false, Utility.EMPTY),
    version("版本号", true, false, Utility.EMPTY),
    license("许可证", true, false, Utility.EMPTY),
    system_logo("系统图标", true, false, Utility.EMPTY),
    system_title("系统标题", true, false, Utility.EMPTY),
    system_admin_contact("系统管理员联系方式", true, false, Utility.EMPTY),

    log_task("输出任务日志", true, false, Utility.EMPTY),
    log_level("日志级别", true, false, Utility.EMPTY),

    ldap_server_json("LDAP服务器JSON", true, false, Utility.EMPTY),
    mail_server_json("邮件服务器JSON", true, false, Utility.EMPTY),
    ftp_server_json("FTP服务器JSON", true, false, Utility.EMPTY),
    reservation_quota_json("预留配额JSON", true, false, Utility.EMPTY),

    statics_page_json("资源概览页面配置", true, false, Utility.EMPTY),

    deploy_task_threshold("部署任务数量阈值", true, false, Utility.EMPTY),
    check_ip_used_by_ping("使用PING检查IP", true, false, Utility.EMPTY),

    expire_months("默认到期时间（月份）", true, false, Utility.EMPTY),
    storage_usage_alert("存储使用率告警", true, false, Utility.EMPTY),

    auto_deploy("自动部署", false, false, Utility.EMPTY),
    auto_audit("自动稽核", false, false, Utility.EMPTY),
    yearly_start_date("财年起始日期", false, false, Utility.EMPTY),

	operation_control("项目发布", true, false, Utility.EMPTY),

	license_config("许可证配置", true, false, Utility.EMPTY);
	
    private final String title;
    private final boolean isSystemOrTenantLevel;
    private final boolean isInternalBatchConfig;
    private final String defaultValue;

    UpSystemConfigKey(String title, boolean isSystemOrTenantLevel, boolean isInternalBatchConfig, String defaultValue) {
        this.title = title;
        this.isSystemOrTenantLevel = isSystemOrTenantLevel;
        this.isInternalBatchConfig = isInternalBatchConfig;
        this.defaultValue = defaultValue;
    }

    public String getTitle() {
        return title;
    }

    public boolean isSystemOrTenantLevel() {
        return isSystemOrTenantLevel;
    }

    public boolean isInternalBatchConfig() {
        return isInternalBatchConfig;
    }

    public String getDefaultValue() {
        return defaultValue;
    }
}
